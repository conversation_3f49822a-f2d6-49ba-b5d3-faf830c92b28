      COMPLEX FUNCTION WCDOTC( N, CX, INCX, CY, INCY )
      INTEGER INCX, INCY, N
      COMPLEX CX(*), CY(*)
      COMPLEX RESULT
      EXTERNAL ACC_CDOTC_SUB
      CALL ACC_CDOTC_SUB( N, CX, INCX, CY, INCY, RESULT )
      WCDOTC = RESULT
      END FUNCTION

      COMPLEX FUNCTION WCDOTU( N, CX, INCX, CY, INCY )
      INTEGER INCX, INCY, N
      COMPLEX CX(*), CY(*)
      COMPLEX RESULT
      EXTERNAL ACC_CDOTU_SUB
      CALL ACC_CDOTU_SUB( N, CX, INCX, CY, INCY, RESULT )
      WCDOTU = RESULT
      END FUNCTION

      DOUBLE COMPLEX FUNCTION WZDOTC( N, CX, INCX, CY, INCY )
      INTEGER INCX, INCY, N
      DOUBLE COMPLEX CX(*), CY(*)
      DOUBLE COMPLEX RESULT
      EXTERNAL ACC_ZDOTC_SUB
      CALL ACC_ZDOTC_SUB( N, CX, INCX, CY, INCY, RESULT )
      WZDOTC = RESULT
      END FUNCTION

      DOUBLE COMPLEX FUNCTION WZDOTU( N, CX, INCX, CY, INCY )
      INTEGER INCX, INCY, N
      DOUBLE COMPLEX CX(*), CY(*)
      DOUBLE COMPLEX RESULT
      EXTERNAL ACC_ZDOTU_SUB
      CALL ACC_ZDOTU_SUB( N, CX, INCX, CY, INCY, RESULT )
      WZDOTU = RESULT
      END FUNCTION

      COMPLEX FUNCTION WCLADIV( X, Y )
      COMPLEX            X, Y
      COMPLEX            Z
      EXTERNAL CLADIV
      CALL CLADIV(Z, X, Y)
      WCLADIV = Z
      END FUNCTION

      DOUBLE COMPLEX FUNCTION WZLADIV( X, Y )
      DOUBLE COMPLEX     X, Y
      DOUBLE COMPLEX     Z
      EXTERNAL ZLADIV
      CALL ZLADIV(Z, X, Y)
      WZLADIV = Z
      END FUNCTION
