{
    // The version of the config file format.  Do not change, unless
    // you know what you are doing.
    "version": 1,

    // The name of the project being benchmarked
    "project": "scipy",

    // The project's homepage
    "project_url": "https://scipy.org/scipylib/",

    // The URL of the source code repository for the project being
    // benchmarked
    "repo": "..",
    "dvcs": "git",
    "branches": ["master"],

    // The base URL to "how a commit for the project.
    "show_commit_url": "https://github.com/scipy/scipy/commit/",

    // The Pythons you'd like to test against.  If not provided, defaults
    // to the current version of Python used to run `asv`.
    "pythons": ["3.6"],

    // The matrix of dependencies to test.  Each key is the name of a
    // package (in PyPI) and the values are version numbers.  An empty
    // list indicates to just test against the default (latest)
    // version.
    "matrix": {
        "numpy": [],
        "Tempita": [],
        "Cython": ["0.29.2"],
        "pytest": [],
        "pybind11": [],
        "six": [],
    },

    // The directory (relative to the current directory) that benchmarks are
    // stored in.  If not provided, defaults to "benchmarks"
    "benchmark_dir": "benchmarks",

    // The directory (relative to the current directory) to cache the Python
    // environments in.  If not provided, defaults to "env"
    "env_dir": "env",

    "environment_type": "virtualenv",
    "wheel_cache_size": 10,

    // The directory (relative to the current directory) that raw benchmark
    // results are stored in.  If not provided, defaults to "results".
    "results_dir": "results",

    // The directory (relative to the current directory) that the html tree
    // should be written to.  If not provided, defaults to "html".
    "html_dir": "html",

    // The number of characters to retain in the commit hashes.
    "hash_length": 8,

    // The commits after which the regression search in `asv publish`
    // should start looking for regressions. Dictionary whose keys are
    // regexps matching to benchmark names, and values corresponding to
    // the commit (exclusive) after which to start looking for
    // regressions.  The default is to start from the first commit
    // with results. If the commit is `null`, regression detection is
    // skipped for the matching benchmark.

    "regressions_first_commits": {
       "io_matlab\\.StructArr\\..*": "67c089a6",  //  structarrs weren't properly implemented before this
    }
}
