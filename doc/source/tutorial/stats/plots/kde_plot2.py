import numpy as np
import matplotlib.pyplot as plt
from scipy import stats

x1 = np.array([-7, -5, 1, 4, 5], dtype=float)
x_eval = np.linspace(-10, 10, num=200)
kde1 = stats.gaussian_kde(x1)
kde2 = stats.gaussian_kde(x1, bw_method='silverman')

def my_kde_bandwidth(obj, fac=1./5):
    """We use <PERSON>'s Rule, multiplied by a constant factor."""
    return np.power(obj.n, -1./(obj.d+4)) * fac


fig = plt.figure()
ax = fig.add_subplot(111)

ax.plot(x1, np.zeros(x1.shape), 'b+', ms=20)  # rug plot
kde3 = stats.gaussian_kde(x1, bw_method=my_kde_bandwidth)
ax.plot(x_eval, kde3(x_eval), 'g-', label="With smaller BW")

plt.show()
