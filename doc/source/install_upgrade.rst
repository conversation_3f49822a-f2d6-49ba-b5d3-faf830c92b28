.. _installing-upgrading:

Installing and upgrading
========================

Information on how to install SciPy and/or the SciPy Stack (a larger set of
packages for scientific computing with Python) can be found at
https://scipy.org/install.html .

It is recommended that users use a scientific Python distribution or binaries
for their platform.  If building from source is required, documentation about
that can be found at :doc:`building/index`.

If you already have SciPy installed and want to upgrade to a newer version, use
the same install mechanism as you have used to install SciPy before. Before
upgrading to a newer version, it is recommended to check that your own code
does not use any deprecated SciPy functionality.  To do so, run your code with
``python -Wd``.
