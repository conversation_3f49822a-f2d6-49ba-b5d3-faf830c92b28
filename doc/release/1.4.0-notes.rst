==========================
SciPy 1.4.0 Release Notes
==========================

.. note:: Scipy 1.4.0 is not released yet!

.. contents::

SciPy 1.4.0 is the culmination of X months of hard work. It contains
many new features, numerous bug-fixes, improved test coverage and better
documentation. There have been a number of deprecations and API changes
in this release, which are documented below. All users are encouraged to
upgrade to this release, as there are a large number of bug-fixes and
optimizations. Before upgrading, we recommend that users check that
their own code does not use deprecated SciPy functionality (to do so,
run your code with ``python -Wd`` and check for ``DeprecationWarning`` s).
Our development attention will now shift to bug-fix releases on the
1.3.x branch, and on adding new features on the master branch.

This release requires Python 3.5+ and NumPy 1.13.3 or greater.

For running on PyPy, PyPy3 6.0+ and NumPy 1.15.0 are required.

Highlights of this release
--------------------------

-


New features
============

`scipy.integrate` improvements
------------------------------

The function `scipy.integrate.solve_ivp` now has an ``args`` argument.
This allows the user-defined functions passed to the function to have
additional parameters without having to create wrapper functions or
lambda expressions for them.

`scipy.interpolate` improvements
--------------------------------

`scipy.io` improvements
-----------------------


`scipy.linalg` improvements
---------------------------


`scipy.ndimage` improvements
----------------------------


`scipy.optimize` improvements
-----------------------------


`scipy.signal` improvements
---------------------------


`scipy.sparse` improvements
---------------------------

`scipy.spatial` improvements
----------------------------

`scipy.stats` improvements
--------------------------

Deprecated features
===================

Backwards incompatible changes
==============================

`scipy.interpolate` changes
---------------------------

`scipy.linalg` changes
----------------------


`scipy.stats` changes
---------------------


Other changes
=============


Authors
=======


Issues closed for 1.4.0
-----------------------

Pull requests for 1.4.0
-----------------------
