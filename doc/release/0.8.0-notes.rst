=========================
SciPy 0.8.0 Release Notes
=========================

.. contents::

SciPy 0.8.0 is the culmination of 17 months of hard work. It contains
many new features, numerous bug-fixes, improved test coverage and
better documentation.  There have been a number of deprecations and
API changes in this release, which are documented below.  All users
are encouraged to upgrade to this release, as there are a large number
of bug-fixes and optimizations.  Moreover, our development attention
will now shift to bug-fix releases on the 0.8.x branch, and on adding
new features on the development trunk.  This release requires Python
2.4 - 2.6 and NumPy 1.4.1 or greater.

Please note that SciPy is still considered to have "Beta" status, as
we work toward a SciPy 1.0.0 release.  The 1.0.0 release will mark a
major milestone in the development of SciPy, after which changing the
package structure or API will be much more difficult.  Whilst these
pre-1.0 releases are considered to have "Beta" status, we are
committed to making them as bug-free as possible.  

However, until the 1.0 release, we are aggressively reviewing and
refining the functionality, organization, and interface. This is being
done in an effort to make the package as coherent, intuitive, and
useful as possible.  To achieve this, we need help from the community
of users.  Specifically, we need feedback regarding all aspects of the
project - everything - from which algorithms we implement, to details
about our function's call signatures.

Python 3
========

Python 3 compatibility is planned and is currently technically
feasible, since Numpy has been ported. However, since the Python 3
compatible Numpy 1.5 has not been released yet, support for Python 3
in Scipy is not yet included in Scipy 0.8.  SciPy 0.9, planned for fall 
2010, will very likely include experimental support for Python 3.

Major documentation improvements
================================

SciPy documentation is greatly improved.

Deprecated features
===================

Swapping inputs for correlation functions (scipy.signal)
--------------------------------------------------------

Concern correlate, correlate2d, convolve and convolve2d. If the second input is
larger than the first input, the inputs are swapped before calling the
underlying computation routine. This behavior is deprecated, and will be
removed in scipy 0.9.0.

Obsolete code deprecated (scipy.misc)
-------------------------------------

The modules `helpmod`, `ppimport` and `pexec` from `scipy.misc` are deprecated.
They will be removed from SciPy in version 0.9.

Additional deprecations
-----------------------

* linalg: The function `solveh_banded` currently returns a tuple containing
  the Cholesky factorization and the solution to the linear system.  In
  SciPy 0.9, the return value will be just the solution.
* The function `constants.codata.find` will generate a DeprecationWarning.
  In Scipy version 0.8.0, the keyword argument 'disp' was added to the
  function, with the default value 'True'.  In 0.9.0, the default will be
  'False'.
* The `qshape` keyword argument of `signal.chirp` is deprecated.  Use
  the argument `vertex_zero` instead.
* Passing the coefficients of a polynomial as the argument `f0` to
  `signal.chirp` is deprecated.  Use the function `signal.sweep_poly`
  instead.
* The `io.recaster` module has been deprecated and will be removed in 0.9.0.

New features
============

DCT support (scipy.fftpack)
---------------------------

New realtransforms have been added, namely dct and idct for Discrete Cosine
Transform; type I, II and III are available.

Single precision support for fft functions (scipy.fftpack)
----------------------------------------------------------

fft functions can now handle single precision inputs as well: fft(x) will
return a single precision array if x is single precision.

At the moment, for FFT sizes that are not composites of 2, 3, and 5, the
transform is computed internally in double precision to avoid rounding error in
FFTPACK.

Correlation functions now implement the usual definition (scipy.signal)
-----------------------------------------------------------------------

The outputs should now correspond to their matlab and R counterparts, and do
what most people expect if the old_behavior=False argument is passed:

* correlate, convolve and their 2d counterparts do not swap their inputs
  depending on their relative shape anymore;
* correlation functions now conjugate their second argument while computing
  the slided sum-products, which correspond to the usual definition of
  correlation.

Additions and modification to LTI functions (scipy.signal)
----------------------------------------------------------

* The functions `impulse2` and `step2` were added to `scipy.signal`.
  They use the function `scipy.signal.lsim2` to compute the impulse and
  step response of a system, respectively.
* The function `scipy.signal.lsim2` was changed to pass any additional
  keyword arguments to the ODE solver.

Improved waveform generators (scipy.signal)
-------------------------------------------

Several improvements to the `chirp` function in `scipy.signal` were made:

* The waveform generated when `method="logarithmic"` was corrected; it
  now generates a waveform that is also known as an "exponential" or
  "geometric" chirp. (See https://en.wikipedia.org/wiki/Chirp.)
* A new `chirp` method, "hyperbolic", was added.
* Instead of the keyword `qshape`, `chirp` now uses the keyword
  `vertex_zero`, a boolean.
* `chirp` no longer handles an arbitrary polynomial.  This functionality
  has been moved to a new function, `sweep_poly`.

A new function, `sweep_poly`, was added.

New functions and other changes in scipy.linalg
-----------------------------------------------

The functions `cho_solve_banded`, `circulant`, `companion`, `hadamard` and
`leslie` were added to `scipy.linalg`.

The function `block_diag` was enhanced to accept scalar and 1D arguments,
along with the usual 2D arguments.

New function and changes in scipy.optimize
------------------------------------------

The `curve_fit` function has been added; it takes a function and uses
non-linear least squares to fit that to the provided data.

The `leastsq` and `fsolve` functions now return an array of size one instead of
a scalar when solving for a single parameter.

New sparse least squares solver
-------------------------------

The `lsqr` function was added to `scipy.sparse`.  `This routine
<https://web.stanford.edu/group/SOL/software/lsqr/>`_ finds a
least-squares solution to a large, sparse, linear system of equations.

ARPACK-based sparse SVD
-----------------------

A naive implementation of SVD for sparse matrices is available in
scipy.sparse.linalg.eigen.arpack. It is based on using an symmetric solver on
<A, A>, and as such may not be very precise.

Alternative behavior available for `scipy.constants.find`
---------------------------------------------------------

The keyword argument `disp` was added to the function `scipy.constants.find`,
with the default value `True`.  When `disp` is `True`, the behavior is the
same as in Scipy version 0.7.  When `False`, the function returns the list of
keys instead of printing them.  (In SciPy version 0.9, the default will be
reversed.)

Incomplete sparse LU decompositions
-----------------------------------

Scipy now wraps SuperLU version 4.0, which supports incomplete sparse LU
decompositions. These can be accessed via `scipy.sparse.linalg.spilu`.
Upgrade to SuperLU 4.0 also fixes some known bugs.

Faster matlab file reader and default behavior change
------------------------------------------------------

We've rewritten the matlab file reader in Cython and it should now read
matlab files at around the same speed that Matlab does.

The reader reads matlab named and anonymous functions, but it can't
write them.

Until scipy 0.8.0 we have returned arrays of matlab structs as numpy
object arrays, where the objects have attributes named for the struct
fields.  As of 0.8.0, we return matlab structs as numpy structured
arrays.  You can get the older behavior by using the optional
``struct_as_record=False`` keyword argument to `scipy.io.loadmat` and
friends.

There is an inconsistency in the matlab file writer, in that it writes
numpy 1D arrays as column vectors in matlab 5 files, and row vectors in
matlab 4 files.  We will change this in the next version, so both write
row vectors.  There is a `FutureWarning` when calling the writer to warn
of this change; for now we suggest using the ``oned_as='row'`` keyword
argument to `scipy.io.savemat` and friends.

Faster evaluation of orthogonal polynomials
-------------------------------------------

Values of orthogonal polynomials can be evaluated with new vectorized functions
in `scipy.special`: `eval_legendre`, `eval_chebyt`, `eval_chebyu`,
`eval_chebyc`, `eval_chebys`, `eval_jacobi`, `eval_laguerre`,
`eval_genlaguerre`, `eval_hermite`, `eval_hermitenorm`,
`eval_gegenbauer`, `eval_sh_legendre`, `eval_sh_chebyt`,
`eval_sh_chebyu`, `eval_sh_jacobi`. This is faster than constructing the
full coefficient representation of the polynomials, which was previously the
only available way.

Note that the previous orthogonal polynomial routines will now also invoke this
feature, when possible.

Lambert W function
------------------

`scipy.special.lambertw` can now be used for evaluating the Lambert W
function.

Improved hypergeometric 2F1 function
------------------------------------

Implementation of `scipy.special.hyp2f1` for real parameters was revised.
The new version should produce accurate values for all real parameters.

More flexible interface for Radial basis function interpolation
---------------------------------------------------------------

The `scipy.interpolate.Rbf` class now accepts a callable as input for the
"function" argument, in addition to the built-in radial basis functions which
can be selected with a string argument.

Removed features
================

scipy.stsci: the package was removed

The module `scipy.misc.limits` was removed.

scipy.io
--------

The IO code in both NumPy and SciPy is being extensively
reworked. NumPy will be where basic code for reading and writing NumPy
arrays is located, while SciPy will house file readers and writers for
various data formats (data, audio, video, images, matlab, etc.).

Several functions in `scipy.io` are removed in the 0.8.0 release including:
`npfile`, `save`, `load`, `create_module`, `create_shelf`,
`objload`, `objsave`, `fopen`, `read_array`, `write_array`,
`fread`, `fwrite`, `bswap`, `packbits`, `unpackbits`, and
`convert_objectarray`.  Some of these functions have been replaced by NumPy's
raw reading and writing capabilities, memory-mapping capabilities, or array
methods.  Others have been moved from SciPy to NumPy, since basic array reading
and writing capability is now handled by NumPy.
